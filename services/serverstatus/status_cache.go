package serverstatus

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ServerStatus 服务器状态
type ServerStatus struct {
	Host         string    `json:"host"`
	Port         int       `json:"port"`
	Status       string    `json:"status"`       // "Success" or "Error"
	LastChecked  time.Time `json:"last_checked"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

// StatusCache 状态缓存管理器
type StatusCache struct {
	logger      *zap.SugaredLogger
	orm         *gorm.DB
	mutex       sync.RWMutex
	sftpCache   map[string]*ServerStatus
	dicomCache  map[string]*ServerStatus
	ctx         context.Context
	cancel      context.CancelFunc
	checkTicker *time.Ticker
}

// NewStatusCache 创建状态缓存管理器
func NewStatusCache() (*StatusCache, error) {
	logger, err := core.GetLogger()
	if err != nil {
		return nil, fmt.Errorf("get logger failed: %v", err)
	}

	orm, err := core.GetDatabase()
	if err != nil {
		return nil, fmt.Errorf("get database failed: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	cache := &StatusCache{
		logger:     logger,
		orm:        orm,
		mutex:      sync.RWMutex{},
		sftpCache:  make(map[string]*ServerStatus),
		dicomCache: make(map[string]*ServerStatus),
		ctx:        ctx,
		cancel:     cancel,
	}

	// 启动定期检查
	cache.startPeriodicCheck()

	return cache, nil
}

// startPeriodicCheck 启动定期检查
func (c *StatusCache) startPeriodicCheck() {
	// 每30秒检查一次
	c.checkTicker = time.NewTicker(30 * time.Second)
	
	go func() {
		defer c.checkTicker.Stop()
		
		// 立即执行一次检查
		c.refreshAllStatus()
		
		for {
			select {
			case <-c.ctx.Done():
				return
			case <-c.checkTicker.C:
				c.refreshAllStatus()
			}
		}
	}()
}

// refreshAllStatus 刷新所有服务器状态
func (c *StatusCache) refreshAllStatus() {
	c.logger.Debugw("starting periodic server status check")
	
	sysConfig := new(model.SysConfig)
	if err := c.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		c.logger.Errorw("failed to get sys config", "error", err)
		return
	}

	// 异步检查SFTP服务器
	go c.checkSftpServers(sysConfig)
	
	// 异步检查DICOM服务器
	go c.checkDicomServers(sysConfig)
}

// checkSftpServers 检查SFTP服务器状态
func (c *StatusCache) checkSftpServers(sysConfig *model.SysConfig) {
	if sysConfig.Sftp == nil {
		return
	}

	var servers []struct {
		host string
		port int
	}

	if sysConfig.Sftp.Auth.ResolveType == "dns" {
		servers = append(servers, struct {
			host string
			port int
		}{
			host: sysConfig.Sftp.Auth.Host,
			port: int(sysConfig.Sftp.Auth.Port),
		})
	} else {
		for _, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
			servers = append(servers, struct {
				host string
				port int
			}{
				host: ipConfig.Host,
				port: int(ipConfig.Port),
			})
		}
	}

	// 并发检查所有服务器
	var wg sync.WaitGroup
	for _, server := range servers {
		wg.Add(1)
		go func(host string, port int) {
			defer wg.Done()
			c.checkSingleServer(host, port, "sftp")
		}(server.host, server.port)
	}
	wg.Wait()
}

// checkDicomServers 检查DICOM服务器状态
func (c *StatusCache) checkDicomServers(sysConfig *model.SysConfig) {
	if sysConfig.Dicom == nil {
		return
	}

	var servers []struct {
		host string
		port int
	}

	if sysConfig.Dicom.Auth.ResolveType == "dns" {
		servers = append(servers, struct {
			host string
			port int
		}{
			host: sysConfig.Dicom.Auth.ServerIp,
			port: int(sysConfig.Dicom.Auth.ServerPort),
		})
	} else {
		for _, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
			servers = append(servers, struct {
				host string
				port int
			}{
				host: ipConfig.ServerIp,
				port: int(ipConfig.ServerPort),
			})
		}
	}

	// 并发检查所有服务器
	var wg sync.WaitGroup
	for _, server := range servers {
		wg.Add(1)
		go func(host string, port int) {
			defer wg.Done()
			c.checkSingleServer(host, port, "dicom")
		}(server.host, server.port)
	}
	wg.Wait()
}

// checkSingleServer 检查单个服务器状态
func (c *StatusCache) checkSingleServer(host string, port int, serverType string) {
	key := fmt.Sprintf("%s:%d", host, port)
	
	status := &ServerStatus{
		Host:        host,
		Port:        port,
		LastChecked: time.Now(),
	}

	// 设置较短的超时时间，避免阻塞
	conn, err := net.DialTimeout("tcp", key, 3*time.Second)
	if err != nil {
		status.Status = "Error"
		status.ErrorMessage = err.Error()
	} else {
		status.Status = "Success"
		conn.Close()
	}

	// 更新缓存
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	if serverType == "sftp" {
		c.sftpCache[key] = status
	} else {
		c.dicomCache[key] = status
	}

	c.logger.Debugw("server status checked", 
		"type", serverType, 
		"host", host, 
		"port", port, 
		"status", status.Status)
}

// GetSftpStatus 获取SFTP服务器状态
func (c *StatusCache) GetSftpStatus(host string, port int) *ServerStatus {
	key := fmt.Sprintf("%s:%d", host, port)
	
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	if status, exists := c.sftpCache[key]; exists {
		// 如果缓存数据太旧（超过5分钟），触发异步刷新
		if time.Since(status.LastChecked) > 5*time.Minute {
			go c.checkSingleServer(host, port, "sftp")
		}
		return status
	}
	
	// 如果缓存中没有，返回默认状态并触发异步检查
	go c.checkSingleServer(host, port, "sftp")
	return &ServerStatus{
		Host:        host,
		Port:        port,
		Status:      "Checking",
		LastChecked: time.Now(),
	}
}

// GetDicomStatus 获取DICOM服务器状态
func (c *StatusCache) GetDicomStatus(host string, port int) *ServerStatus {
	key := fmt.Sprintf("%s:%d", host, port)
	
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	if status, exists := c.dicomCache[key]; exists {
		// 如果缓存数据太旧（超过5分钟），触发异步刷新
		if time.Since(status.LastChecked) > 5*time.Minute {
			go c.checkSingleServer(host, port, "dicom")
		}
		return status
	}
	
	// 如果缓存中没有，返回默认状态并触发异步检查
	go c.checkSingleServer(host, port, "dicom")
	return &ServerStatus{
		Host:        host,
		Port:        port,
		Status:      "Checking",
		LastChecked: time.Now(),
	}
}

// TriggerConfigUpdate 配置更新时触发立即检查
func (c *StatusCache) TriggerConfigUpdate() {
	c.logger.Infow("config updated, triggering immediate server status check")
	go c.refreshAllStatus()
}

// Stop 停止状态缓存服务
func (c *StatusCache) Stop() {
	if c.cancel != nil {
		c.cancel()
	}
	if c.checkTicker != nil {
		c.checkTicker.Stop()
	}
}
