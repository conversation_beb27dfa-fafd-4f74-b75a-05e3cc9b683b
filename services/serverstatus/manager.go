package serverstatus

import (
	"sync"
)

var (
	globalCache *StatusCache
	once        sync.Once
	initErr     error
)

// GetGlobalStatusCache 获取全局状态缓存实例
func GetGlobalStatusCache() (*StatusCache, error) {
	once.Do(func() {
		globalCache, initErr = NewStatusCache()
	})
	return globalCache, initErr
}

// StopGlobalStatusCache 停止全局状态缓存服务
func StopGlobalStatusCache() {
	if globalCache != nil {
		globalCache.Stop()
	}
}
